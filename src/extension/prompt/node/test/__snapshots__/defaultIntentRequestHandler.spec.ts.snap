// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`defaultIntentRequestHandler > ChatResult metadata after multiple turns only has tool results from current turn 1`] = `
{
  "codeBlocks": [],
  "toolCallResults": {
    "tool_call_id_0__vscode-0": LanguageModelToolResult {
      "content": [
        LanguageModelTextPart {
          "value": "tool-result-0",
        },
      ],
    },
    "tool_call_id_1__vscode-1": LanguageModelToolResult {
      "content": [
        LanguageModelTextPart {
          "value": "tool-result-1",
        },
      ],
    },
  },
  "toolCallRounds": [
    ToolCallRound {
      "id": "static-id",
      "response": "response number 0",
      "toolCalls": [
        {
          "arguments": "some args here",
          "id": "tool_call_id_0__vscode-0",
          "name": "my_func",
        },
      ],
      "toolInputRetry": 0,
    },
    ToolCallRound {
      "id": "static-id",
      "response": "response number 1",
      "toolCalls": [
        {
          "arguments": "some args here",
          "id": "tool_call_id_1__vscode-1",
          "name": "my_func",
        },
      ],
      "toolInputRetry": 0,
    },
    ToolCallRound {
      "id": "static-id",
      "response": "response number 2",
      "toolCalls": [],
      "toolInputRetry": 0,
    },
  ],
}
`;

exports[`defaultIntentRequestHandler > ChatResult metadata after multiple turns only has tool results from current turn 2`] = `
{
  "codeBlocks": [],
  "toolCallResults": {
    "tool_call_id_2__vscode-2": LanguageModelToolResult {
      "content": [
        LanguageModelTextPart {
          "value": "tool-result-2",
        },
      ],
    },
    "tool_call_id_3__vscode-3": LanguageModelToolResult {
      "content": [
        LanguageModelTextPart {
          "value": "tool-result-3",
        },
      ],
    },
  },
  "toolCallRounds": [
    ToolCallRound {
      "id": "static-id",
      "response": "response number 3",
      "toolCalls": [
        {
          "arguments": "some args here",
          "id": "tool_call_id_2__vscode-2",
          "name": "my_func",
        },
      ],
      "toolInputRetry": 0,
    },
    ToolCallRound {
      "id": "static-id",
      "response": "response number 4",
      "toolCalls": [
        {
          "arguments": "some args here",
          "id": "tool_call_id_3__vscode-3",
          "name": "my_func",
        },
      ],
      "toolInputRetry": 0,
    },
    ToolCallRound {
      "id": "static-id",
      "response": "response number 5",
      "toolCalls": [],
      "toolInputRetry": 0,
    },
  ],
}
`;

exports[`defaultIntentRequestHandler > avoids requests when handler return is null 1`] = `
{
  "telemetrySenderEvents": [],
  "telemetryServiceEvents": [
    {
      "eventName": "interactiveSessionMessage",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "contextTypes": "none",
        "detectedIntent": "none",
        "intent": "test",
        "isParticipantDetected": "false",
        "query": "hello world!",
      },
    },
  ],
}
`;

exports[`defaultIntentRequestHandler > confirms on max tool call iterations, and continues to iterate 1`] = `
{
  "metadata": {
    "codeBlocks": [],
    "maxToolCallsExceeded": true,
    "toolCallResults": {
      "tool_call_id_0__vscode-0": LanguageModelToolResult {
        "content": [
          LanguageModelTextPart {
            "value": "tool-result-0",
          },
        ],
      },
      "tool_call_id_1__vscode-1": LanguageModelToolResult {
        "content": [
          LanguageModelTextPart {
            "value": "tool-result-1",
          },
        ],
      },
      "tool_call_id_2__vscode-2": LanguageModelToolResult {
        "content": [
          LanguageModelTextPart {
            "value": "tool-result-2",
          },
        ],
      },
      "tool_call_id_3__vscode-3": LanguageModelToolResult {
        "content": [
          LanguageModelTextPart {
            "value": "tool-result-3",
          },
        ],
      },
    },
    "toolCallRounds": [
      ToolCallRound {
        "id": "static-id",
        "response": "response number 0",
        "toolCalls": [
          {
            "arguments": "some args here",
            "id": "tool_call_id_0__vscode-0",
            "name": "my_func",
          },
        ],
        "toolInputRetry": 0,
      },
      ToolCallRound {
        "id": "static-id",
        "response": "response number 1",
        "toolCalls": [
          {
            "arguments": "some args here",
            "id": "tool_call_id_1__vscode-1",
            "name": "my_func",
          },
        ],
        "toolInputRetry": 0,
      },
      ToolCallRound {
        "id": "static-id",
        "response": "response number 2",
        "toolCalls": [
          {
            "arguments": "some args here",
            "id": "tool_call_id_2__vscode-2",
            "name": "my_func",
          },
        ],
        "toolInputRetry": 0,
      },
      ToolCallRound {
        "id": "static-id",
        "response": "response number 3",
        "toolCalls": [
          {
            "arguments": "some args here",
            "id": "tool_call_id_3__vscode-3",
            "name": "my_func",
          },
        ],
        "toolInputRetry": 0,
      },
    ],
  },
}
`;

exports[`defaultIntentRequestHandler > confirms on max tool call iterations, and continues to iterate 2`] = `
{
  "metadata": {
    "codeBlocks": [],
    "maxToolCallsExceeded": true,
    "toolCallResults": {
      "tool_call_id_4__vscode-4": LanguageModelToolResult {
        "content": [
          LanguageModelTextPart {
            "value": "tool-result-4",
          },
        ],
      },
      "tool_call_id_5__vscode-5": LanguageModelToolResult {
        "content": [
          LanguageModelTextPart {
            "value": "tool-result-5",
          },
        ],
      },
      "tool_call_id_6__vscode-6": LanguageModelToolResult {
        "content": [
          LanguageModelTextPart {
            "value": "tool-result-6",
          },
        ],
      },
      "tool_call_id_7__vscode-7": LanguageModelToolResult {
        "content": [
          LanguageModelTextPart {
            "value": "tool-result-7",
          },
        ],
      },
    },
    "toolCallRounds": [
      ToolCallRound {
        "id": "static-id",
        "response": "response number 4",
        "toolCalls": [
          {
            "arguments": "some args here",
            "id": "tool_call_id_4__vscode-4",
            "name": "my_func",
          },
        ],
        "toolInputRetry": 0,
      },
      ToolCallRound {
        "id": "static-id",
        "response": "response number 5",
        "toolCalls": [
          {
            "arguments": "some args here",
            "id": "tool_call_id_5__vscode-5",
            "name": "my_func",
          },
        ],
        "toolInputRetry": 0,
      },
      ToolCallRound {
        "id": "static-id",
        "response": "response number 6",
        "toolCalls": [
          {
            "arguments": "some args here",
            "id": "tool_call_id_6__vscode-6",
            "name": "my_func",
          },
        ],
        "toolInputRetry": 0,
      },
      ToolCallRound {
        "id": "static-id",
        "response": "response number 7",
        "toolCalls": [
          {
            "arguments": "some args here",
            "id": "tool_call_id_7__vscode-7",
            "name": "my_func",
          },
        ],
        "toolInputRetry": 0,
      },
    ],
  },
}
`;

exports[`defaultIntentRequestHandler > confirms on max tool call iterations, and continues to iterate 3`] = `
[
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "response number ",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "0",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "response number ",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "1",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "response number ",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "2",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "response number ",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "3",
      },
    },
  },
  ChatResponseConfirmationPart {
    "buttons": [
      "Continue",
      "Pause",
    ],
    "data": {
      "copilotRequestedRoundLimit": 5,
    },
    "message": "Copilot has been working on this problem for a while. It can continue to iterate, or you can send a new message to refine your prompt.",
    "title": "Continue to iterate?",
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "response number ",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "4",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "response number ",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "5",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "response number ",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "6",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "response number ",
      },
    },
  },
  ChatResponseMarkdownPart {
    "value": MarkdownString {
      "delegate": MarkdownString {
        "baseUri": undefined,
        "isTrusted": undefined,
        "supportHtml": false,
        "supportThemeIcons": false,
        "value": "7",
      },
    },
  },
  ChatResponseConfirmationPart {
    "buttons": [
      "Continue",
      "Pause",
    ],
    "data": {
      "copilotRequestedRoundLimit": 5,
    },
    "message": "Copilot has been working on this problem for a while. It can continue to iterate, or you can send a new message to refine your prompt.",
    "title": "Continue to iterate?",
  },
]
`;

exports[`defaultIntentRequestHandler > confirms on max tool call iterations, and continues to iterate 4`] = `
{
  "telemetrySenderEvents": [],
  "telemetryServiceEvents": [
    {
      "eventName": "interactiveSessionMessage",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "contextTypes": "none",
        "detectedIntent": "none",
        "intent": "test",
        "isParticipantDetected": "false",
        "query": "hello world!",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "hello world!",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 17,
        "numCodeBlocks": 0,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "messageText": "response number 0",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "panel.request",
      "eventType": "default",
      "measurements": {
        "availableToolCount": 0,
        "codeGenInstructionFileCount": 0,
        "codeGenInstructionSettingsCount": 0,
        "codeGenInstructionsCount": 0,
        "codeGenInstructionsFilteredCount": 0,
        "codeGenInstructionsLength": 0,
        "links": 0,
        "maybeOffTopic": 0,
        "messageTokenCount": 3,
        "numToolCalls": 1,
        "promptTokenCount": 10,
        "responseTokenCount": 4,
        "temporalCtxFileCount": -1,
        "temporalCtxTotalCharCount": -1,
        "textBlocks": 1,
        "timeToComplete": "<duration>",
        "timeToFirstToken": "<duration>",
        "timeToRequest": "<duration>",
        "turn": 1,
        "userPromptCount": 1,
      },
      "properties": {
        "codeBlocks": "",
        "command": "test",
        "contextTypes": "none",
        "conversationId": "some-session-id",
        "isParticipantDetected": "false",
        "languageId": undefined,
        "model": "gpt-4.1-2025-04-14",
        "promptTypes": "1:1",
        "requestId": "turn-id-0",
        "responseId": "turn-id-0",
        "responseType": "success",
        "toolCounts": "{"my_func":1}",
      },
    },
    {
      "eventName": "interactiveSessionResponse",
      "eventType": "internal",
      "measurements": {
        "turnNumber": 1,
      },
      "properties": {
        "baseModel": "gpt-4.1-2025-04-14",
        "chatLocation": "panel",
        "intent": "test",
        "isParticipantDetected": "false",
        "request": "hello world!",
        "response": "response number 0",
        "sessionId": "some-session-id",
      },
    },
    {
      "eventName": "interactiveSessionMessage",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "contextTypes": "none",
        "detectedIntent": "none",
        "intent": "test",
        "isParticipantDetected": "false",
        "query": "hello world!",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "hello world!",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 17,
        "numCodeBlocks": 0,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "messageText": "response number 1",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "panel.request",
      "eventType": "default",
      "measurements": {
        "availableToolCount": 0,
        "codeGenInstructionFileCount": 0,
        "codeGenInstructionSettingsCount": 0,
        "codeGenInstructionsCount": 0,
        "codeGenInstructionsFilteredCount": 0,
        "codeGenInstructionsLength": 0,
        "links": 0,
        "maybeOffTopic": 0,
        "messageTokenCount": 3,
        "numToolCalls": 1,
        "promptTokenCount": 10,
        "responseTokenCount": 4,
        "temporalCtxFileCount": -1,
        "temporalCtxTotalCharCount": -1,
        "textBlocks": 1,
        "timeToComplete": "<duration>",
        "timeToFirstToken": "<duration>",
        "timeToRequest": "<duration>",
        "turn": 1,
        "userPromptCount": 1,
      },
      "properties": {
        "codeBlocks": "",
        "command": "test",
        "contextTypes": "none",
        "conversationId": "some-session-id",
        "isParticipantDetected": "false",
        "languageId": undefined,
        "model": "gpt-4.1-2025-04-14",
        "promptTypes": "1:1",
        "requestId": "turn-id-0",
        "responseId": "turn-id-0",
        "responseType": "success",
        "toolCounts": "{"my_func":1}",
      },
    },
    {
      "eventName": "interactiveSessionResponse",
      "eventType": "internal",
      "measurements": {
        "turnNumber": 1,
      },
      "properties": {
        "baseModel": "gpt-4.1-2025-04-14",
        "chatLocation": "panel",
        "intent": "test",
        "isParticipantDetected": "false",
        "request": "hello world!",
        "response": "response number 1",
        "sessionId": "some-session-id",
      },
    },
    {
      "eventName": "interactiveSessionMessage",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "contextTypes": "none",
        "detectedIntent": "none",
        "intent": "test",
        "isParticipantDetected": "false",
        "query": "hello world!",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "hello world!",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 17,
        "numCodeBlocks": 0,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "messageText": "response number 2",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "panel.request",
      "eventType": "default",
      "measurements": {
        "availableToolCount": 0,
        "codeGenInstructionFileCount": 0,
        "codeGenInstructionSettingsCount": 0,
        "codeGenInstructionsCount": 0,
        "codeGenInstructionsFilteredCount": 0,
        "codeGenInstructionsLength": 0,
        "links": 0,
        "maybeOffTopic": 0,
        "messageTokenCount": 3,
        "numToolCalls": 1,
        "promptTokenCount": 10,
        "responseTokenCount": 4,
        "temporalCtxFileCount": -1,
        "temporalCtxTotalCharCount": -1,
        "textBlocks": 1,
        "timeToComplete": "<duration>",
        "timeToFirstToken": "<duration>",
        "timeToRequest": "<duration>",
        "turn": 1,
        "userPromptCount": 1,
      },
      "properties": {
        "codeBlocks": "",
        "command": "test",
        "contextTypes": "none",
        "conversationId": "some-session-id",
        "isParticipantDetected": "false",
        "languageId": undefined,
        "model": "gpt-4.1-2025-04-14",
        "promptTypes": "1:1",
        "requestId": "turn-id-0",
        "responseId": "turn-id-0",
        "responseType": "success",
        "toolCounts": "{"my_func":1}",
      },
    },
    {
      "eventName": "interactiveSessionResponse",
      "eventType": "internal",
      "measurements": {
        "turnNumber": 1,
      },
      "properties": {
        "baseModel": "gpt-4.1-2025-04-14",
        "chatLocation": "panel",
        "intent": "test",
        "isParticipantDetected": "false",
        "request": "hello world!",
        "response": "response number 2",
        "sessionId": "some-session-id",
      },
    },
    {
      "eventName": "interactiveSessionMessage",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "contextTypes": "none",
        "detectedIntent": "none",
        "intent": "test",
        "isParticipantDetected": "false",
        "query": "hello world!",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "hello world!",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 17,
        "numCodeBlocks": 0,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "messageText": "response number 3",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "panel.request",
      "eventType": "default",
      "measurements": {
        "availableToolCount": 0,
        "codeGenInstructionFileCount": 0,
        "codeGenInstructionSettingsCount": 0,
        "codeGenInstructionsCount": 0,
        "codeGenInstructionsFilteredCount": 0,
        "codeGenInstructionsLength": 0,
        "links": 0,
        "maybeOffTopic": 0,
        "messageTokenCount": 3,
        "numToolCalls": 1,
        "promptTokenCount": 10,
        "responseTokenCount": 4,
        "temporalCtxFileCount": -1,
        "temporalCtxTotalCharCount": -1,
        "textBlocks": 1,
        "timeToComplete": "<duration>",
        "timeToFirstToken": "<duration>",
        "timeToRequest": "<duration>",
        "turn": 1,
        "userPromptCount": 1,
      },
      "properties": {
        "codeBlocks": "",
        "command": "test",
        "contextTypes": "none",
        "conversationId": "some-session-id",
        "isParticipantDetected": "false",
        "languageId": undefined,
        "model": "gpt-4.1-2025-04-14",
        "promptTypes": "1:1",
        "requestId": "turn-id-0",
        "responseId": "turn-id-0",
        "responseType": "success",
        "toolCounts": "{"my_func":1}",
      },
    },
    {
      "eventName": "interactiveSessionResponse",
      "eventType": "internal",
      "measurements": {
        "turnNumber": 1,
      },
      "properties": {
        "baseModel": "gpt-4.1-2025-04-14",
        "chatLocation": "panel",
        "intent": "test",
        "isParticipantDetected": "false",
        "request": "hello world!",
        "response": "response number 3",
        "sessionId": "some-session-id",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 17,
        "numCodeBlocks": 0,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "source": "model",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "response number 3",
        "source": "model",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "interactiveSessionMessage",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "contextTypes": "none",
        "detectedIntent": "none",
        "intent": "test",
        "isParticipantDetected": "false",
        "query": "hello world!",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "hello world!",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 17,
        "numCodeBlocks": 0,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "messageText": "response number 4",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "panel.request",
      "eventType": "default",
      "measurements": {
        "availableToolCount": 0,
        "codeGenInstructionFileCount": 0,
        "codeGenInstructionSettingsCount": 0,
        "codeGenInstructionsCount": 0,
        "codeGenInstructionsFilteredCount": 0,
        "codeGenInstructionsLength": 0,
        "links": 0,
        "maybeOffTopic": 0,
        "messageTokenCount": 3,
        "numToolCalls": 1,
        "promptTokenCount": 10,
        "responseTokenCount": 4,
        "temporalCtxFileCount": -1,
        "temporalCtxTotalCharCount": -1,
        "textBlocks": 1,
        "timeToComplete": "<duration>",
        "timeToFirstToken": "<duration>",
        "timeToRequest": "<duration>",
        "turn": 1,
        "userPromptCount": 1,
      },
      "properties": {
        "codeBlocks": "",
        "command": "test",
        "contextTypes": "none",
        "conversationId": "some-session-id",
        "isParticipantDetected": "false",
        "languageId": undefined,
        "model": "gpt-4.1-2025-04-14",
        "promptTypes": "1:1",
        "requestId": "turn-id-0",
        "responseId": "turn-id-0",
        "responseType": "success",
        "toolCounts": "{"my_func":1}",
      },
    },
    {
      "eventName": "interactiveSessionResponse",
      "eventType": "internal",
      "measurements": {
        "turnNumber": 1,
      },
      "properties": {
        "baseModel": "gpt-4.1-2025-04-14",
        "chatLocation": "panel",
        "intent": "test",
        "isParticipantDetected": "false",
        "request": "hello world!",
        "response": "response number 4",
        "sessionId": "some-session-id",
      },
    },
    {
      "eventName": "interactiveSessionMessage",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "contextTypes": "none",
        "detectedIntent": "none",
        "intent": "test",
        "isParticipantDetected": "false",
        "query": "hello world!",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "hello world!",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 17,
        "numCodeBlocks": 0,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "messageText": "response number 5",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "panel.request",
      "eventType": "default",
      "measurements": {
        "availableToolCount": 0,
        "codeGenInstructionFileCount": 0,
        "codeGenInstructionSettingsCount": 0,
        "codeGenInstructionsCount": 0,
        "codeGenInstructionsFilteredCount": 0,
        "codeGenInstructionsLength": 0,
        "links": 0,
        "maybeOffTopic": 0,
        "messageTokenCount": 3,
        "numToolCalls": 1,
        "promptTokenCount": 10,
        "responseTokenCount": 4,
        "temporalCtxFileCount": -1,
        "temporalCtxTotalCharCount": -1,
        "textBlocks": 1,
        "timeToComplete": "<duration>",
        "timeToFirstToken": "<duration>",
        "timeToRequest": "<duration>",
        "turn": 1,
        "userPromptCount": 1,
      },
      "properties": {
        "codeBlocks": "",
        "command": "test",
        "contextTypes": "none",
        "conversationId": "some-session-id",
        "isParticipantDetected": "false",
        "languageId": undefined,
        "model": "gpt-4.1-2025-04-14",
        "promptTypes": "1:1",
        "requestId": "turn-id-0",
        "responseId": "turn-id-0",
        "responseType": "success",
        "toolCounts": "{"my_func":1}",
      },
    },
    {
      "eventName": "interactiveSessionResponse",
      "eventType": "internal",
      "measurements": {
        "turnNumber": 1,
      },
      "properties": {
        "baseModel": "gpt-4.1-2025-04-14",
        "chatLocation": "panel",
        "intent": "test",
        "isParticipantDetected": "false",
        "request": "hello world!",
        "response": "response number 5",
        "sessionId": "some-session-id",
      },
    },
    {
      "eventName": "interactiveSessionMessage",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "contextTypes": "none",
        "detectedIntent": "none",
        "intent": "test",
        "isParticipantDetected": "false",
        "query": "hello world!",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "hello world!",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 17,
        "numCodeBlocks": 0,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "messageText": "response number 6",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "panel.request",
      "eventType": "default",
      "measurements": {
        "availableToolCount": 0,
        "codeGenInstructionFileCount": 0,
        "codeGenInstructionSettingsCount": 0,
        "codeGenInstructionsCount": 0,
        "codeGenInstructionsFilteredCount": 0,
        "codeGenInstructionsLength": 0,
        "links": 0,
        "maybeOffTopic": 0,
        "messageTokenCount": 3,
        "numToolCalls": 1,
        "promptTokenCount": 10,
        "responseTokenCount": 4,
        "temporalCtxFileCount": -1,
        "temporalCtxTotalCharCount": -1,
        "textBlocks": 1,
        "timeToComplete": "<duration>",
        "timeToFirstToken": "<duration>",
        "timeToRequest": "<duration>",
        "turn": 1,
        "userPromptCount": 1,
      },
      "properties": {
        "codeBlocks": "",
        "command": "test",
        "contextTypes": "none",
        "conversationId": "some-session-id",
        "isParticipantDetected": "false",
        "languageId": undefined,
        "model": "gpt-4.1-2025-04-14",
        "promptTypes": "1:1",
        "requestId": "turn-id-0",
        "responseId": "turn-id-0",
        "responseType": "success",
        "toolCounts": "{"my_func":1}",
      },
    },
    {
      "eventName": "interactiveSessionResponse",
      "eventType": "internal",
      "measurements": {
        "turnNumber": 1,
      },
      "properties": {
        "baseModel": "gpt-4.1-2025-04-14",
        "chatLocation": "panel",
        "intent": "test",
        "isParticipantDetected": "false",
        "request": "hello world!",
        "response": "response number 6",
        "sessionId": "some-session-id",
      },
    },
    {
      "eventName": "interactiveSessionMessage",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "contextTypes": "none",
        "detectedIntent": "none",
        "intent": "test",
        "isParticipantDetected": "false",
        "query": "hello world!",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "hello world!",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 17,
        "numCodeBlocks": 0,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "messageText": "response number 7",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "panel.request",
      "eventType": "default",
      "measurements": {
        "availableToolCount": 0,
        "codeGenInstructionFileCount": 0,
        "codeGenInstructionSettingsCount": 0,
        "codeGenInstructionsCount": 0,
        "codeGenInstructionsFilteredCount": 0,
        "codeGenInstructionsLength": 0,
        "links": 0,
        "maybeOffTopic": 0,
        "messageTokenCount": 3,
        "numToolCalls": 1,
        "promptTokenCount": 10,
        "responseTokenCount": 4,
        "temporalCtxFileCount": -1,
        "temporalCtxTotalCharCount": -1,
        "textBlocks": 1,
        "timeToComplete": "<duration>",
        "timeToFirstToken": "<duration>",
        "timeToRequest": "<duration>",
        "turn": 1,
        "userPromptCount": 1,
      },
      "properties": {
        "codeBlocks": "",
        "command": "test",
        "contextTypes": "none",
        "conversationId": "some-session-id",
        "isParticipantDetected": "false",
        "languageId": undefined,
        "model": "gpt-4.1-2025-04-14",
        "promptTypes": "1:1",
        "requestId": "turn-id-0",
        "responseId": "turn-id-0",
        "responseType": "success",
        "toolCounts": "{"my_func":1}",
      },
    },
    {
      "eventName": "interactiveSessionResponse",
      "eventType": "internal",
      "measurements": {
        "turnNumber": 1,
      },
      "properties": {
        "baseModel": "gpt-4.1-2025-04-14",
        "chatLocation": "panel",
        "intent": "test",
        "isParticipantDetected": "false",
        "request": "hello world!",
        "response": "response number 7",
        "sessionId": "some-session-id",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 17,
        "numCodeBlocks": 0,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "source": "model",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "response number 7",
        "source": "model",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
  ],
}
`;

exports[`defaultIntentRequestHandler > makes a successful request with a single turn 1`] = `
{
  "metadata": {
    "codeBlocks": [],
    "toolCallResults": undefined,
    "toolCallRounds": [
      ToolCallRound {
        "id": "static-id",
        "response": "some response here :)",
        "toolCalls": [],
        "toolInputRetry": 0,
      },
    ],
  },
}
`;

exports[`defaultIntentRequestHandler > makes a successful request with a single turn 2`] = `
{
  "telemetrySenderEvents": [],
  "telemetryServiceEvents": [
    {
      "eventName": "interactiveSessionMessage",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "contextTypes": "none",
        "detectedIntent": "none",
        "intent": "test",
        "isParticipantDetected": "false",
        "query": "hello world!",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "hello world!",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 21,
        "numCodeBlocks": 0,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "messageText": "some response here :)",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "panel.request",
      "eventType": "default",
      "measurements": {
        "availableToolCount": 0,
        "codeGenInstructionFileCount": 0,
        "codeGenInstructionSettingsCount": 0,
        "codeGenInstructionsCount": 0,
        "codeGenInstructionsFilteredCount": 0,
        "codeGenInstructionsLength": 0,
        "links": 0,
        "maybeOffTopic": 0,
        "messageTokenCount": 3,
        "numToolCalls": 0,
        "promptTokenCount": 10,
        "responseTokenCount": 4,
        "temporalCtxFileCount": -1,
        "temporalCtxTotalCharCount": -1,
        "textBlocks": 1,
        "timeToComplete": "<duration>",
        "timeToFirstToken": "<duration>",
        "timeToRequest": "<duration>",
        "turn": 1,
        "userPromptCount": 1,
      },
      "properties": {
        "codeBlocks": "",
        "command": "test",
        "contextTypes": "none",
        "conversationId": "some-session-id",
        "isParticipantDetected": "false",
        "languageId": undefined,
        "model": "gpt-4.1-2025-04-14",
        "promptTypes": "1:1",
        "requestId": "turn-id-0",
        "responseId": "turn-id-0",
        "responseType": "success",
        "toolCounts": "{}",
      },
    },
    {
      "eventName": "interactiveSessionResponse",
      "eventType": "internal",
      "measurements": {
        "turnNumber": 1,
      },
      "properties": {
        "baseModel": "gpt-4.1-2025-04-14",
        "chatLocation": "panel",
        "intent": "test",
        "isParticipantDetected": "false",
        "request": "hello world!",
        "response": "some response here :)",
        "sessionId": "some-session-id",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 21,
        "numCodeBlocks": 0,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "source": "model",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "some response here :)",
        "source": "model",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
  ],
}
`;

exports[`defaultIntentRequestHandler > makes a tool call turn 1`] = `
{
  "metadata": {
    "codeBlocks": [],
    "toolCallResults": {
      "tool_call_id__vscode-0": LanguageModelToolResult {
        "content": [
          LanguageModelTextPart {
            "value": "tool-result",
          },
        ],
      },
    },
    "toolCallRounds": [
      ToolCallRound {
        "id": "static-id",
        "response": "some response here :)",
        "toolCalls": [
          {
            "arguments": "some args here",
            "id": "tool_call_id__vscode-0",
            "name": "my_func",
          },
        ],
        "toolInputRetry": 0,
      },
      ToolCallRound {
        "id": "static-id",
        "response": "response to tool call",
        "toolCalls": [],
        "toolInputRetry": 0,
      },
    ],
  },
}
`;

exports[`defaultIntentRequestHandler > makes a tool call turn 2`] = `
{
  "telemetrySenderEvents": [],
  "telemetryServiceEvents": [
    {
      "eventName": "interactiveSessionMessage",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "contextTypes": "none",
        "detectedIntent": "none",
        "intent": "test",
        "isParticipantDetected": "false",
        "query": "hello world!",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "hello world!",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 21,
        "numCodeBlocks": 0,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "messageText": "some response here :)",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "panel.request",
      "eventType": "default",
      "measurements": {
        "availableToolCount": 0,
        "codeGenInstructionFileCount": 0,
        "codeGenInstructionSettingsCount": 0,
        "codeGenInstructionsCount": 0,
        "codeGenInstructionsFilteredCount": 0,
        "codeGenInstructionsLength": 0,
        "links": 0,
        "maybeOffTopic": 0,
        "messageTokenCount": 3,
        "numToolCalls": 1,
        "promptTokenCount": 10,
        "responseTokenCount": 4,
        "temporalCtxFileCount": -1,
        "temporalCtxTotalCharCount": -1,
        "textBlocks": 1,
        "timeToComplete": "<duration>",
        "timeToFirstToken": "<duration>",
        "timeToRequest": "<duration>",
        "turn": 1,
        "userPromptCount": 1,
      },
      "properties": {
        "codeBlocks": "",
        "command": "test",
        "contextTypes": "none",
        "conversationId": "some-session-id",
        "isParticipantDetected": "false",
        "languageId": undefined,
        "model": "gpt-4.1-2025-04-14",
        "promptTypes": "1:1",
        "requestId": "turn-id-0",
        "responseId": "turn-id-0",
        "responseType": "success",
        "toolCounts": "{"my_func":1}",
      },
    },
    {
      "eventName": "interactiveSessionResponse",
      "eventType": "internal",
      "measurements": {
        "turnNumber": 1,
      },
      "properties": {
        "baseModel": "gpt-4.1-2025-04-14",
        "chatLocation": "panel",
        "intent": "test",
        "isParticipantDetected": "false",
        "request": "hello world!",
        "response": "some response here :)",
        "sessionId": "some-session-id",
      },
    },
    {
      "eventName": "interactiveSessionMessage",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "contextTypes": "none",
        "detectedIntent": "none",
        "intent": "test",
        "isParticipantDetected": "false",
        "query": "hello world!",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "hello world!",
        "offTopic": "false",
        "source": "user",
        "suggestion": "test",
        "turnIndex": "0",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 21,
        "numCodeBlocks": 0,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {
        "messageCharLen": 12,
        "promptTokenLen": 10,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "some-uuid",
        "messageId": "some-uuid",
        "messageText": "response to tool call",
        "replyType": "none",
        "source": "model",
        "suggestion": "test",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "panel.request",
      "eventType": "default",
      "measurements": {
        "availableToolCount": 0,
        "codeGenInstructionFileCount": 0,
        "codeGenInstructionSettingsCount": 0,
        "codeGenInstructionsCount": 0,
        "codeGenInstructionsFilteredCount": 0,
        "codeGenInstructionsLength": 0,
        "links": 0,
        "maybeOffTopic": 0,
        "messageTokenCount": 3,
        "numToolCalls": 0,
        "promptTokenCount": 10,
        "responseTokenCount": 4,
        "temporalCtxFileCount": -1,
        "temporalCtxTotalCharCount": -1,
        "textBlocks": 1,
        "timeToComplete": "<duration>",
        "timeToFirstToken": "<duration>",
        "timeToRequest": "<duration>",
        "turn": 1,
        "userPromptCount": 1,
      },
      "properties": {
        "codeBlocks": "",
        "command": "test",
        "contextTypes": "none",
        "conversationId": "some-session-id",
        "isParticipantDetected": "false",
        "languageId": undefined,
        "model": "gpt-4.1-2025-04-14",
        "promptTypes": "1:1",
        "requestId": "turn-id-0",
        "responseId": "turn-id-0",
        "responseType": "success",
        "toolCounts": "{}",
      },
    },
    {
      "eventName": "interactiveSessionResponse",
      "eventType": "internal",
      "measurements": {
        "turnNumber": 1,
      },
      "properties": {
        "baseModel": "gpt-4.1-2025-04-14",
        "chatLocation": "panel",
        "intent": "test",
        "isParticipantDetected": "false",
        "request": "hello world!",
        "response": "response to tool call",
        "sessionId": "some-session-id",
      },
    },
    {
      "eventName": "conversation.message",
      "eventType": "default",
      "measurements": {
        "messageCharLen": 21,
        "numCodeBlocks": 0,
      },
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "source": "model",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
    {
      "eventName": "conversation.messageText",
      "eventType": "internal",
      "measurements": {},
      "properties": {
        "codeBlockLanguages": "{}",
        "conversationId": "some-session-id",
        "headerRequestId": "",
        "messageId": "some-uuid",
        "messageText": "response to tool call",
        "source": "model",
        "turnIndex": "1",
        "uiKind": "conversationPanel",
      },
    },
  ],
}
`;
